<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.resources.absensi-resource.pages.create-absensi' => 'App\\Filament\\Resources\\AbsensiResource\\Pages\\CreateAbsensi',
    'app.filament.resources.absensi-resource.pages.edit-absensi' => 'App\\Filament\\Resources\\AbsensiResource\\Pages\\EditAbsensi',
    'app.filament.resources.absensi-resource.pages.list-absensis' => 'App\\Filament\\Resources\\AbsensiResource\\Pages\\ListAbsensis',
    'app.filament.resources.absensi-resource.pages.view-absensi' => 'App\\Filament\\Resources\\AbsensiResource\\Pages\\ViewAbsensi',
    'app.filament.resources.akun-resource.pages.create-akun' => 'App\\Filament\\Resources\\AkunResource\\Pages\\CreateAkun',
    'app.filament.resources.akun-resource.pages.edit-akun' => 'App\\Filament\\Resources\\AkunResource\\Pages\\EditAkun',
    'app.filament.resources.akun-resource.pages.list-akuns' => 'App\\Filament\\Resources\\AkunResource\\Pages\\ListAkuns',
    'app.filament.resources.aset-resource.pages.create-aset' => 'App\\Filament\\Resources\\AsetResource\\Pages\\CreateAset',
    'app.filament.resources.aset-resource.pages.edit-aset' => 'App\\Filament\\Resources\\AsetResource\\Pages\\EditAset',
    'app.filament.resources.aset-resource.pages.list-asets' => 'App\\Filament\\Resources\\AsetResource\\Pages\\ListAsets',
    'app.filament.resources.aturan-keterlambatan-resource.pages.create-aturan-keterlambatan' => 'App\\Filament\\Resources\\AturanKeterlambatanResource\\Pages\\CreateAturanKeterlambatan',
    'app.filament.resources.aturan-keterlambatan-resource.pages.edit-aturan-keterlambatan' => 'App\\Filament\\Resources\\AturanKeterlambatanResource\\Pages\\EditAturanKeterlambatan',
    'app.filament.resources.aturan-keterlambatan-resource.pages.list-aturan-keterlambatans' => 'App\\Filament\\Resources\\AturanKeterlambatanResource\\Pages\\ListAturanKeterlambatans',
    'app.filament.resources.aturan-keterlambatan-resource.pages.view-aturan-keterlambatan' => 'App\\Filament\\Resources\\AturanKeterlambatanResource\\Pages\\ViewAturanKeterlambatan',
    'app.filament.resources.bpjs-relation-manager-resource.relation-managers.karyawan-resource-relation-manager' => 'App\\Filament\\Resources\\BpjsRelationManagerResource\\RelationManagers\\KaryawanResourceRelationManager',
    'app.filament.resources.company-settings-resource.pages.create-company-settings' => 'App\\Filament\\Resources\\CompanySettingsResource\\Pages\\CreateCompanySettings',
    'app.filament.resources.company-settings-resource.pages.edit-company-settings' => 'App\\Filament\\Resources\\CompanySettingsResource\\Pages\\EditCompanySettings',
    'app.filament.resources.company-settings-resource.pages.list-company-settings' => 'App\\Filament\\Resources\\CompanySettingsResource\\Pages\\ListCompanySettings',
    'app.filament.resources.cuti-izin-resource.pages.create-cuti-izin' => 'App\\Filament\\Resources\\CutiIzinResource\\Pages\\CreateCutiIzin',
    'app.filament.resources.cuti-izin-resource.pages.edit-cuti-izin' => 'App\\Filament\\Resources\\CutiIzinResource\\Pages\\EditCutiIzin',
    'app.filament.resources.cuti-izin-resource.pages.list-cuti-izins' => 'App\\Filament\\Resources\\CutiIzinResource\\Pages\\ListCutiIzins',
    'app.filament.resources.cuti-izin-resource.pages.view-cuti-izin' => 'App\\Filament\\Resources\\CutiIzinResource\\Pages\\ViewCutiIzin',
    'app.filament.resources.departemen-resource.pages.create-departemen' => 'App\\Filament\\Resources\\DepartemenResource\\Pages\\CreateDepartemen',
    'app.filament.resources.departemen-resource.pages.edit-departemen' => 'App\\Filament\\Resources\\DepartemenResource\\Pages\\EditDepartemen',
    'app.filament.resources.departemen-resource.pages.list-departemens' => 'App\\Filament\\Resources\\DepartemenResource\\Pages\\ListDepartemens',
    'app.filament.resources.divisi-resource.pages.create-divisi' => 'App\\Filament\\Resources\\DivisiResource\\Pages\\CreateDivisi',
    'app.filament.resources.divisi-resource.pages.edit-divisi' => 'App\\Filament\\Resources\\DivisiResource\\Pages\\EditDivisi',
    'app.filament.resources.divisi-resource.pages.list-divisis' => 'App\\Filament\\Resources\\DivisiResource\\Pages\\ListDivisis',
    'app.filament.resources.entitas-resource.pages.create-entitas' => 'App\\Filament\\Resources\\EntitasResource\\Pages\\CreateEntitas',
    'app.filament.resources.entitas-resource.pages.edit-entitas' => 'App\\Filament\\Resources\\EntitasResource\\Pages\\EditEntitas',
    'app.filament.resources.entitas-resource.pages.list-entitas' => 'App\\Filament\\Resources\\EntitasResource\\Pages\\ListEntitas',
    'app.filament.resources.expense-category-resource.pages.create-expense-category' => 'App\\Filament\\Resources\\ExpenseCategoryResource\\Pages\\CreateExpenseCategory',
    'app.filament.resources.expense-category-resource.pages.edit-expense-category' => 'App\\Filament\\Resources\\ExpenseCategoryResource\\Pages\\EditExpenseCategory',
    'app.filament.resources.expense-category-resource.pages.list-expense-categories' => 'App\\Filament\\Resources\\ExpenseCategoryResource\\Pages\\ListExpenseCategories',
    'app.filament.resources.expense-category-resource.pages.view-expense-category' => 'App\\Filament\\Resources\\ExpenseCategoryResource\\Pages\\ViewExpenseCategory',
    'app.filament.resources.expense-request-resource.pages.create-expense-request' => 'App\\Filament\\Resources\\ExpenseRequestResource\\Pages\\CreateExpenseRequest',
    'app.filament.resources.expense-request-resource.pages.edit-expense-request' => 'App\\Filament\\Resources\\ExpenseRequestResource\\Pages\\EditExpenseRequest',
    'app.filament.resources.expense-request-resource.pages.list-expense-requests' => 'App\\Filament\\Resources\\ExpenseRequestResource\\Pages\\ListExpenseRequests',
    'app.filament.resources.expense-request-resource.pages.view-expense-request' => 'App\\Filament\\Resources\\ExpenseRequestResource\\Pages\\ViewExpenseRequest',
    'app.filament.resources.expense-request-resource.relation-managers.expense-request-items-relation-manager' => 'App\\Filament\\Resources\\ExpenseRequestResource\\RelationManagers\\ExpenseRequestItemsRelationManager',
    'app.filament.resources.goods-receipt-resource.pages.create-goods-receipt' => 'App\\Filament\\Resources\\GoodsReceiptResource\\Pages\\CreateGoodsReceipt',
    'app.filament.resources.goods-receipt-resource.pages.edit-goods-receipt' => 'App\\Filament\\Resources\\GoodsReceiptResource\\Pages\\EditGoodsReceipt',
    'app.filament.resources.goods-receipt-resource.pages.list-goods-receipts' => 'App\\Filament\\Resources\\GoodsReceiptResource\\Pages\\ListGoodsReceipts',
    'app.filament.resources.goods-receipt-resource.pages.view-goods-receipt' => 'App\\Filament\\Resources\\GoodsReceiptResource\\Pages\\ViewGoodsReceipt',
    'app.filament.resources.goods-receipt-resource.relation-managers.goods-receipt-items-relation-manager' => 'App\\Filament\\Resources\\GoodsReceiptResource\\RelationManagers\\GoodsReceiptItemsRelationManager',
    'app.filament.resources.inventory-resource.pages.create-inventory' => 'App\\Filament\\Resources\\InventoryResource\\Pages\\CreateInventory',
    'app.filament.resources.inventory-resource.pages.edit-inventory' => 'App\\Filament\\Resources\\InventoryResource\\Pages\\EditInventory',
    'app.filament.resources.inventory-resource.pages.list-inventories' => 'App\\Filament\\Resources\\InventoryResource\\Pages\\ListInventories',
    'app.filament.resources.inventory-stock-resource.pages.create-inventory-stock' => 'App\\Filament\\Resources\\InventoryStockResource\\Pages\\CreateInventoryStock',
    'app.filament.resources.inventory-stock-resource.pages.edit-inventory-stock' => 'App\\Filament\\Resources\\InventoryStockResource\\Pages\\EditInventoryStock',
    'app.filament.resources.inventory-stock-resource.pages.list-inventory-stocks' => 'App\\Filament\\Resources\\InventoryStockResource\\Pages\\ListInventoryStocks',
    'app.filament.resources.jabatan-resource.pages.create-jabatan' => 'App\\Filament\\Resources\\JabatanResource\\Pages\\CreateJabatan',
    'app.filament.resources.jabatan-resource.pages.edit-jabatan' => 'App\\Filament\\Resources\\JabatanResource\\Pages\\EditJabatan',
    'app.filament.resources.jabatan-resource.pages.list-jabatans' => 'App\\Filament\\Resources\\JabatanResource\\Pages\\ListJabatans',
    'app.filament.resources.jadwal-kerja-resource.pages.create-jadwal-kerja' => 'App\\Filament\\Resources\\JadwalKerjaResource\\Pages\\CreateJadwalKerja',
    'app.filament.resources.jadwal-kerja-resource.pages.edit-jadwal-kerja' => 'App\\Filament\\Resources\\JadwalKerjaResource\\Pages\\EditJadwalKerja',
    'app.filament.resources.jadwal-kerja-resource.pages.list-jadwal-kerjas' => 'App\\Filament\\Resources\\JadwalKerjaResource\\Pages\\ListJadwalKerjas',
    'app.filament.resources.jadwal-kerja-resource.pages.view-jadwal-kerja' => 'App\\Filament\\Resources\\JadwalKerjaResource\\Pages\\ViewJadwalKerja',
    'app.filament.resources.jadwal-kerja-resource.widgets.jadwal-kerja-overview' => 'App\\Filament\\Resources\\JadwalKerjaResource\\Widgets\\JadwalKerjaOverview',
    'app.filament.resources.jadwal-masal-resource.pages.create-jadwal-masal' => 'App\\Filament\\Resources\\JadwalMasalResource\\Pages\\CreateJadwalMasal',
    'app.filament.resources.jadwal-masal-resource.pages.edit-jadwal-masal' => 'App\\Filament\\Resources\\JadwalMasalResource\\Pages\\EditJadwalMasal',
    'app.filament.resources.jadwal-masal-resource.pages.list-jadwal-masals' => 'App\\Filament\\Resources\\JadwalMasalResource\\Pages\\ListJadwalMasals',
    'app.filament.resources.jenis-pelanggaran-resource.pages.create-jenis-pelanggaran' => 'App\\Filament\\Resources\\JenisPelanggaranResource\\Pages\\CreateJenisPelanggaran',
    'app.filament.resources.jenis-pelanggaran-resource.pages.edit-jenis-pelanggaran' => 'App\\Filament\\Resources\\JenisPelanggaranResource\\Pages\\EditJenisPelanggaran',
    'app.filament.resources.jenis-pelanggaran-resource.pages.list-jenis-pelanggarans' => 'App\\Filament\\Resources\\JenisPelanggaranResource\\Pages\\ListJenisPelanggarans',
    'app.filament.resources.jenis-pelanggaran-resource.pages.view-jenis-pelanggaran' => 'App\\Filament\\Resources\\JenisPelanggaranResource\\Pages\\ViewJenisPelanggaran',
    'app.filament.resources.journal-resource.pages.create-journal' => 'App\\Filament\\Resources\\JournalResource\\Pages\\CreateJournal',
    'app.filament.resources.journal-resource.pages.edit-journal' => 'App\\Filament\\Resources\\JournalResource\\Pages\\EditJournal',
    'app.filament.resources.journal-resource.pages.list-journals' => 'App\\Filament\\Resources\\JournalResource\\Pages\\ListJournals',
    'app.filament.resources.journal-resource.pages.view-journal' => 'App\\Filament\\Resources\\JournalResource\\Pages\\ViewJournal',
    'app.filament.resources.karyawan-resource.pages.create-karyawan' => 'App\\Filament\\Resources\\KaryawanResource\\Pages\\CreateKaryawan',
    'app.filament.resources.karyawan-resource.pages.edit-karyawan' => 'App\\Filament\\Resources\\KaryawanResource\\Pages\\EditKaryawan',
    'app.filament.resources.karyawan-resource.pages.list-karyawans' => 'App\\Filament\\Resources\\KaryawanResource\\Pages\\ListKaryawans',
    'app.filament.resources.karyawan-resource.pages.view-karyawan' => 'App\\Filament\\Resources\\KaryawanResource\\Pages\\ViewKaryawan',
    'app.filament.resources.karyawan-resource.relation-managers.absensi-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\AbsensiRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.bpjs-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\BpjsRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.cuti-izin-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\CutiIzinRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.dokumen-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\DokumenRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.kerabat-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\KerabatRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.kpi-penilaian-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\KpiPenilaianRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.mutasi-promosi-demosi-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\MutasiPromosiDemosiRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.pelanggaran-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\PelanggaranRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.pendidikan-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\PendidikanRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.penggajian-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\PenggajianRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.resign-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\ResignRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.riwayat-kontrak-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\RiwayatKontrakRelationManager',
    'app.filament.resources.karyawan-resource.relation-managers.schedules-relation-manager' => 'App\\Filament\\Resources\\KaryawanResource\\RelationManagers\\SchedulesRelationManager',
    'app.filament.resources.karyawan-resource.widgets.karyawan-alerts-widget' => 'App\\Filament\\Resources\\KaryawanResource\\Widgets\\KaryawanAlertsWidget',
    'app.filament.resources.karyawan-resource.widgets.karyawan-demographics-widget' => 'App\\Filament\\Resources\\KaryawanResource\\Widgets\\KaryawanDemographicsWidget',
    'app.filament.resources.karyawan-resource.widgets.karyawan-detail-summary' => 'App\\Filament\\Resources\\KaryawanResource\\Widgets\\KaryawanDetailSummary',
    'app.filament.resources.karyawan-resource.widgets.karyawan-performance-widget' => 'App\\Filament\\Resources\\KaryawanResource\\Widgets\\KaryawanPerformanceWidget',
    'app.filament.resources.karyawan-resource.widgets.karyawan-salary-widget' => 'App\\Filament\\Resources\\KaryawanResource\\Widgets\\KaryawanSalaryWidget',
    'app.filament.resources.karyawan-resource.widgets.karyawan-stats-widget' => 'App\\Filament\\Resources\\KaryawanResource\\Widgets\\KaryawanStatsWidget',
    'app.filament.resources.karyawan-resource.widgets.karyawan-summary' => 'App\\Filament\\Resources\\KaryawanResource\\Widgets\\KaryawanSummary',
    'app.filament.resources.karyawan-resource.widgets.kontrak-aktif-summary' => 'App\\Filament\\Resources\\KaryawanResource\\Widgets\\KontrakAktifSummary',
    'app.filament.resources.payroll-component-resource.pages.create-payroll-component' => 'App\\Filament\\Resources\\PayrollComponentResource\\Pages\\CreatePayrollComponent',
    'app.filament.resources.payroll-component-resource.pages.edit-payroll-component' => 'App\\Filament\\Resources\\PayrollComponentResource\\Pages\\EditPayrollComponent',
    'app.filament.resources.payroll-component-resource.pages.list-payroll-components' => 'App\\Filament\\Resources\\PayrollComponentResource\\Pages\\ListPayrollComponents',
    'app.filament.resources.payroll-component-resource.pages.view-payroll-component' => 'App\\Filament\\Resources\\PayrollComponentResource\\Pages\\ViewPayrollComponent',
    'app.filament.resources.payroll-period-resource.pages.create-payroll-period' => 'App\\Filament\\Resources\\PayrollPeriodResource\\Pages\\CreatePayrollPeriod',
    'app.filament.resources.payroll-period-resource.pages.edit-payroll-period' => 'App\\Filament\\Resources\\PayrollPeriodResource\\Pages\\EditPayrollPeriod',
    'app.filament.resources.payroll-period-resource.pages.list-payroll-periods' => 'App\\Filament\\Resources\\PayrollPeriodResource\\Pages\\ListPayrollPeriods',
    'app.filament.resources.payroll-period-resource.pages.process-payroll-period' => 'App\\Filament\\Resources\\PayrollPeriodResource\\Pages\\ProcessPayrollPeriod',
    'app.filament.resources.payroll-period-resource.pages.view-payroll-period' => 'App\\Filament\\Resources\\PayrollPeriodResource\\Pages\\ViewPayrollPeriod',
    'app.filament.resources.payroll-transaction-resource.pages.list-payroll-transactions' => 'App\\Filament\\Resources\\PayrollTransactionResource\\Pages\\ListPayrollTransactions',
    'app.filament.resources.payroll-transaction-resource.pages.view-payroll-transaction' => 'App\\Filament\\Resources\\PayrollTransactionResource\\Pages\\ViewPayrollTransaction',
    'app.filament.resources.payroll-transaction-resource.relation-managers.payroll-deductions-relation-manager' => 'App\\Filament\\Resources\\PayrollTransactionResource\\RelationManagers\\PayrollDeductionsRelationManager',
    'app.filament.resources.petty-cash-fund-resource.pages.create-petty-cash-fund' => 'App\\Filament\\Resources\\PettyCashFundResource\\Pages\\CreatePettyCashFund',
    'app.filament.resources.petty-cash-fund-resource.pages.edit-petty-cash-fund' => 'App\\Filament\\Resources\\PettyCashFundResource\\Pages\\EditPettyCashFund',
    'app.filament.resources.petty-cash-fund-resource.pages.list-petty-cash-funds' => 'App\\Filament\\Resources\\PettyCashFundResource\\Pages\\ListPettyCashFunds',
    'app.filament.resources.petty-cash-fund-resource.pages.view-petty-cash-fund' => 'App\\Filament\\Resources\\PettyCashFundResource\\Pages\\ViewPettyCashFund',
    'app.filament.resources.petty-cash-fund-resource.relation-managers.petty-cash-transactions-relation-manager' => 'App\\Filament\\Resources\\PettyCashFundResource\\RelationManagers\\PettyCashTransactionsRelationManager',
    'app.filament.resources.posting-rule-resource.pages.create-posting-rule' => 'App\\Filament\\Resources\\PostingRuleResource\\Pages\\CreatePostingRule',
    'app.filament.resources.posting-rule-resource.pages.edit-posting-rule' => 'App\\Filament\\Resources\\PostingRuleResource\\Pages\\EditPostingRule',
    'app.filament.resources.posting-rule-resource.pages.list-posting-rules' => 'App\\Filament\\Resources\\PostingRuleResource\\Pages\\ListPostingRules',
    'app.filament.resources.produk-resource.pages.create-produk' => 'App\\Filament\\Resources\\ProdukResource\\Pages\\CreateProduk',
    'app.filament.resources.produk-resource.pages.edit-produk' => 'App\\Filament\\Resources\\ProdukResource\\Pages\\EditProduk',
    'app.filament.resources.produk-resource.pages.list-produks' => 'App\\Filament\\Resources\\ProdukResource\\Pages\\ListProduks',
    'app.filament.resources.project-resource.pages.create-project' => 'App\\Filament\\Resources\\ProjectResource\\Pages\\CreateProject',
    'app.filament.resources.project-resource.pages.edit-project' => 'App\\Filament\\Resources\\ProjectResource\\Pages\\EditProject',
    'app.filament.resources.project-resource.pages.list-projects' => 'App\\Filament\\Resources\\ProjectResource\\Pages\\ListProjects',
    'app.filament.resources.project-resource.pages.view-project' => 'App\\Filament\\Resources\\ProjectResource\\Pages\\ViewProject',
    'app.filament.resources.project-resource.relation-managers.activities-relation-manager' => 'App\\Filament\\Resources\\ProjectResource\\RelationManagers\\ActivitiesRelationManager',
    'app.filament.resources.project-resource.relation-managers.members-relation-manager' => 'App\\Filament\\Resources\\ProjectResource\\RelationManagers\\MembersRelationManager',
    'app.filament.resources.project-resource.relation-managers.tasks-relation-manager' => 'App\\Filament\\Resources\\ProjectResource\\RelationManagers\\TasksRelationManager',
    'app.filament.resources.ptkp-rate-resource.pages.create-ptkp-rate' => 'App\\Filament\\Resources\\PtkpRateResource\\Pages\\CreatePtkpRate',
    'app.filament.resources.ptkp-rate-resource.pages.edit-ptkp-rate' => 'App\\Filament\\Resources\\PtkpRateResource\\Pages\\EditPtkpRate',
    'app.filament.resources.ptkp-rate-resource.pages.list-ptkp-rates' => 'App\\Filament\\Resources\\PtkpRateResource\\Pages\\ListPtkpRates',
    'app.filament.resources.ptkp-rate-resource.pages.view-ptkp-rate' => 'App\\Filament\\Resources\\PtkpRateResource\\Pages\\ViewPtkpRate',
    'app.filament.resources.purchase-order-resource.pages.create-purchase-order' => 'App\\Filament\\Resources\\PurchaseOrderResource\\Pages\\CreatePurchaseOrder',
    'app.filament.resources.purchase-order-resource.pages.edit-purchase-order' => 'App\\Filament\\Resources\\PurchaseOrderResource\\Pages\\EditPurchaseOrder',
    'app.filament.resources.purchase-order-resource.pages.list-purchase-orders' => 'App\\Filament\\Resources\\PurchaseOrderResource\\Pages\\ListPurchaseOrders',
    'app.filament.resources.purchase-order-resource.pages.view-purchase-order' => 'App\\Filament\\Resources\\PurchaseOrderResource\\Pages\\ViewPurchaseOrder',
    'app.filament.resources.purchase-order-resource.relation-managers.purchase-order-items-relation-manager' => 'App\\Filament\\Resources\\PurchaseOrderResource\\RelationManagers\\PurchaseOrderItemsRelationManager',
    'app.filament.resources.role-resource.pages.create-role' => 'App\\Filament\\Resources\\RoleResource\\Pages\\CreateRole',
    'app.filament.resources.role-resource.pages.edit-role' => 'App\\Filament\\Resources\\RoleResource\\Pages\\EditRole',
    'app.filament.resources.role-resource.pages.list-roles' => 'App\\Filament\\Resources\\RoleResource\\Pages\\ListRoles',
    'app.filament.resources.role-resource.pages.view-role' => 'App\\Filament\\Resources\\RoleResource\\Pages\\ViewRole',
    'app.filament.resources.sales-transaction-resource.pages.create-sales-transaction' => 'App\\Filament\\Resources\\SalesTransactionResource\\Pages\\CreateSalesTransaction',
    'app.filament.resources.sales-transaction-resource.pages.edit-sales-transaction' => 'App\\Filament\\Resources\\SalesTransactionResource\\Pages\\EditSalesTransaction',
    'app.filament.resources.sales-transaction-resource.pages.list-sales-transactions' => 'App\\Filament\\Resources\\SalesTransactionResource\\Pages\\ListSalesTransactions',
    'app.filament.resources.sales-transaction-resource.relation-managers.sale-items-relation-manager' => 'App\\Filament\\Resources\\SalesTransactionResource\\RelationManagers\\SaleItemsRelationManager',
    'app.filament.resources.satuan-resource.pages.create-satuan' => 'App\\Filament\\Resources\\SatuanResource\\Pages\\CreateSatuan',
    'app.filament.resources.satuan-resource.pages.edit-satuan' => 'App\\Filament\\Resources\\SatuanResource\\Pages\\EditSatuan',
    'app.filament.resources.satuan-resource.pages.list-satuans' => 'App\\Filament\\Resources\\SatuanResource\\Pages\\ListSatuans',
    'app.filament.resources.shift-resource.pages.create-shift' => 'App\\Filament\\Resources\\ShiftResource\\Pages\\CreateShift',
    'app.filament.resources.shift-resource.pages.edit-shift' => 'App\\Filament\\Resources\\ShiftResource\\Pages\\EditShift',
    'app.filament.resources.shift-resource.pages.list-shifts' => 'App\\Filament\\Resources\\ShiftResource\\Pages\\ListShifts',
    'app.filament.resources.sop-dokumen-resource.pages.create-sop-dokumen' => 'App\\Filament\\Resources\\SopDokumenResource\\Pages\\CreateSopDokumen',
    'app.filament.resources.sop-dokumen-resource.pages.edit-sop-dokumen' => 'App\\Filament\\Resources\\SopDokumenResource\\Pages\\EditSopDokumen',
    'app.filament.resources.sop-dokumen-resource.pages.list-sop-dokumens' => 'App\\Filament\\Resources\\SopDokumenResource\\Pages\\ListSopDokumens',
    'app.filament.resources.sop-dokumen-resource.pages.view-sop-dokumen' => 'App\\Filament\\Resources\\SopDokumenResource\\Pages\\ViewSopDokumen',
    'app.filament.resources.stock-adjustment-resource.pages.create-stock-adjustment' => 'App\\Filament\\Resources\\StockAdjustmentResource\\Pages\\CreateStockAdjustment',
    'app.filament.resources.stock-adjustment-resource.pages.edit-stock-adjustment' => 'App\\Filament\\Resources\\StockAdjustmentResource\\Pages\\EditStockAdjustment',
    'app.filament.resources.stock-adjustment-resource.pages.list-stock-adjustments' => 'App\\Filament\\Resources\\StockAdjustmentResource\\Pages\\ListStockAdjustments',
    'app.filament.resources.stock-movement-resource.pages.list-stock-movements' => 'App\\Filament\\Resources\\StockMovementResource\\Pages\\ListStockMovements',
    'app.filament.resources.stock-opname-resource.pages.create-stock-opname' => 'App\\Filament\\Resources\\StockOpnameResource\\Pages\\CreateStockOpname',
    'app.filament.resources.stock-opname-resource.pages.edit-stock-opname' => 'App\\Filament\\Resources\\StockOpnameResource\\Pages\\EditStockOpname',
    'app.filament.resources.stock-opname-resource.pages.list-stock-opnames' => 'App\\Filament\\Resources\\StockOpnameResource\\Pages\\ListStockOpnames',
    'app.filament.resources.stock-opname-resource.relation-managers.stock-opname-items-relation-manager' => 'App\\Filament\\Resources\\StockOpnameResource\\RelationManagers\\StockOpnameItemsRelationManager',
    'app.filament.resources.struktur-organisasi-resource.pages.list-struktur-organisasi' => 'App\\Filament\\Resources\\StrukturOrganisasiResource\\Pages\\ListStrukturOrganisasi',
    'app.filament.resources.struktur-organisasi-resource.pages.view-struktur-organisasi' => 'App\\Filament\\Resources\\StrukturOrganisasiResource\\Pages\\ViewStrukturOrganisasi',
    'app.filament.resources.struktur-organisasi-resource.widgets.organisasi-stats-widget' => 'App\\Filament\\Resources\\StrukturOrganisasiResource\\Widgets\\OrganisasiStatsWidget',
    'app.filament.resources.supplier-resource.pages.create-supplier' => 'App\\Filament\\Resources\\SupplierResource\\Pages\\CreateSupplier',
    'app.filament.resources.supplier-resource.pages.edit-supplier' => 'App\\Filament\\Resources\\SupplierResource\\Pages\\EditSupplier',
    'app.filament.resources.supplier-resource.pages.list-suppliers' => 'App\\Filament\\Resources\\SupplierResource\\Pages\\ListSuppliers',
    'app.filament.resources.supplier-resource.pages.view-supplier' => 'App\\Filament\\Resources\\SupplierResource\\Pages\\ViewSupplier',
    'app.filament.resources.supplier-resource.relation-managers.purchase-orders-relation-manager' => 'App\\Filament\\Resources\\SupplierResource\\RelationManagers\\PurchaseOrdersRelationManager',
    'app.filament.resources.task-resource.pages.create-task' => 'App\\Filament\\Resources\\TaskResource\\Pages\\CreateTask',
    'app.filament.resources.task-resource.pages.edit-task' => 'App\\Filament\\Resources\\TaskResource\\Pages\\EditTask',
    'app.filament.resources.task-resource.pages.list-tasks' => 'App\\Filament\\Resources\\TaskResource\\Pages\\ListTasks',
    'app.filament.resources.task-resource.pages.view-task' => 'App\\Filament\\Resources\\TaskResource\\Pages\\ViewTask',
    'app.filament.resources.task-resource.relation-managers.comments-relation-manager' => 'App\\Filament\\Resources\\TaskResource\\RelationManagers\\CommentsRelationManager',
    'app.filament.resources.task-resource.relation-managers.timesheets-relation-manager' => 'App\\Filament\\Resources\\TaskResource\\RelationManagers\\TimesheetsRelationManager',
    'app.filament.resources.tax-bracket-resource.pages.create-tax-bracket' => 'App\\Filament\\Resources\\TaxBracketResource\\Pages\\CreateTaxBracket',
    'app.filament.resources.tax-bracket-resource.pages.edit-tax-bracket' => 'App\\Filament\\Resources\\TaxBracketResource\\Pages\\EditTaxBracket',
    'app.filament.resources.tax-bracket-resource.pages.list-tax-brackets' => 'App\\Filament\\Resources\\TaxBracketResource\\Pages\\ListTaxBrackets',
    'app.filament.resources.tax-bracket-resource.pages.view-tax-bracket' => 'App\\Filament\\Resources\\TaxBracketResource\\Pages\\ViewTaxBracket',
    'app.filament.resources.unit-resource.pages.create-unit' => 'App\\Filament\\Resources\\UnitResource\\Pages\\CreateUnit',
    'app.filament.resources.unit-resource.pages.edit-unit' => 'App\\Filament\\Resources\\UnitResource\\Pages\\EditUnit',
    'app.filament.resources.unit-resource.pages.list-units' => 'App\\Filament\\Resources\\UnitResource\\Pages\\ListUnits',
    'app.filament.resources.user-resource.pages.create-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\CreateUser',
    'app.filament.resources.user-resource.pages.edit-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\EditUser',
    'app.filament.resources.user-resource.pages.list-users' => 'App\\Filament\\Resources\\UserResource\\Pages\\ListUsers',
    'app.filament.resources.warehouse-resource.pages.create-warehouse' => 'App\\Filament\\Resources\\WarehouseResource\\Pages\\CreateWarehouse',
    'app.filament.resources.warehouse-resource.pages.edit-warehouse' => 'App\\Filament\\Resources\\WarehouseResource\\Pages\\EditWarehouse',
    'app.filament.resources.warehouse-resource.pages.list-warehouses' => 'App\\Filament\\Resources\\WarehouseResource\\Pages\\ListWarehouses',
    'app.filament.pages.general-ledger' => 'App\\Filament\\Pages\\GeneralLedger',
    'app.filament.pages.absensi-dashboard' => 'App\\Filament\\Pages\\AbsensiDashboard',
    'app.filament.pages.balance-sheet' => 'App\\Filament\\Pages\\BalanceSheet',
    'app.filament.pages.dashboard' => 'App\\Filament\\Pages\\Dashboard',
    'app.filament.pages.enhanced-kanban-board' => 'App\\Filament\\Pages\\EnhancedKanbanBoard',
    'app.filament.pages.enhanced-project-dashboard' => 'App\\Filament\\Pages\\EnhancedProjectDashboard',
    'app.filament.pages.h-r-dashboard' => 'App\\Filament\\Pages\\HRDashboard',
    'app.filament.pages.import-sales' => 'App\\Filament\\Pages\\ImportSales',
    'app.filament.pages.income-statement' => 'App\\Filament\\Pages\\IncomeStatement',
    'app.filament.pages.kanban-board' => 'App\\Filament\\Pages\\KanbanBoard',
    'app.filament.pages.payroll-dashboard' => 'App\\Filament\\Pages\\PayrollDashboard',
    'app.filament.pages.performance-dashboard' => 'App\\Filament\\Pages\\PerformanceDashboard',
    'app.filament.pages.project-overview' => 'App\\Filament\\Pages\\ProjectOverview',
    'app.filament.pages.supervisor-dashboard' => 'App\\Filament\\Pages\\SupervisorDashboard',
    'app.filament.pages.timesheets' => 'App\\Filament\\Pages\\Timesheets',
    'app.filament.pages.work-period-settings' => 'App\\Filament\\Pages\\WorkPeriodSettings',
    'app.filament.widgets.absensi-overview-widget' => 'App\\Filament\\Widgets\\AbsensiOverviewWidget',
    'app.filament.widgets.absensi-status-widget' => 'App\\Filament\\Widgets\\AbsensiStatusWidget',
    'app.filament.widgets.absensi-trend-chart' => 'App\\Filament\\Widgets\\AbsensiTrendChart',
    'app.filament.widgets.attendance-alerts-widget' => 'App\\Filament\\Widgets\\AttendanceAlertsWidget',
    'app.filament.widgets.attendance-analytics-widget' => 'App\\Filament\\Widgets\\AttendanceAnalyticsWidget',
    'app.filament.widgets.attendance-by-department-widget' => 'App\\Filament\\Widgets\\AttendanceByDepartmentWidget',
    'app.filament.widgets.attendance-overview' => 'App\\Filament\\Widgets\\AttendanceOverview',
    'app.filament.widgets.attendance-trends-widget' => 'App\\Filament\\Widgets\\AttendanceTrendsWidget',
    'app.filament.widgets.compensation-breakdown-widget' => 'App\\Filament\\Widgets\\CompensationBreakdownWidget',
    'app.filament.widgets.employee-demographics-widget' => 'App\\Filament\\Widgets\\EmployeeDemographicsWidget',
    'app.filament.widgets.enhanced-project-stats' => 'App\\Filament\\Widgets\\EnhancedProjectStats',
    'app.filament.widgets.h-r-alerts-widget' => 'App\\Filament\\Widgets\\HRAlertsWidget',
    'app.filament.widgets.h-r-overview-widget' => 'App\\Filament\\Widgets\\HROverviewWidget',
    'app.filament.widgets.hr-department-chart' => 'App\\Filament\\Widgets\\HrDepartmentChart',
    'app.filament.widgets.hr-stats-widget' => 'App\\Filament\\Widgets\\HrStatsWidget',
    'app.filament.widgets.inventory-stats-widget' => 'App\\Filament\\Widgets\\InventoryStatsWidget',
    'app.filament.widgets.late-arrivals-widget' => 'App\\Filament\\Widgets\\LateArrivalsWidget',
    'app.filament.widgets.payroll-alerts-widget' => 'App\\Filament\\Widgets\\PayrollAlertsWidget',
    'app.filament.widgets.payroll-by-department-widget' => 'App\\Filament\\Widgets\\PayrollByDepartmentWidget',
    'app.filament.widgets.payroll-overview-widget' => 'App\\Filament\\Widgets\\PayrollOverviewWidget',
    'app.filament.widgets.payroll-stats-overview' => 'App\\Filament\\Widgets\\PayrollStatsOverview',
    'app.filament.widgets.payroll-trend-chart' => 'App\\Filament\\Widgets\\PayrollTrendChart',
    'app.filament.widgets.payroll-trends-widget' => 'App\\Filament\\Widgets\\PayrollTrendsWidget',
    'app.filament.widgets.performance-analytics-widget' => 'App\\Filament\\Widgets\\PerformanceAnalyticsWidget',
    'app.filament.widgets.performance-grade-chart' => 'App\\Filament\\Widgets\\PerformanceGradeChart',
    'app.filament.widgets.performance-overview-widget' => 'App\\Filament\\Widgets\\PerformanceOverviewWidget',
    'app.filament.widgets.project-calendar-view' => 'App\\Filament\\Widgets\\ProjectCalendarView',
    'app.filament.widgets.project-gantt-chart' => 'App\\Filament\\Widgets\\ProjectGanttChart',
    'app.filament.widgets.project-health-overview' => 'App\\Filament\\Widgets\\ProjectHealthOverview',
    'app.filament.widgets.project-management-widget' => 'App\\Filament\\Widgets\\ProjectManagementWidget',
    'app.filament.widgets.project-overview-stats' => 'App\\Filament\\Widgets\\ProjectOverviewStats',
    'app.filament.widgets.project-resource-allocation' => 'App\\Filament\\Widgets\\ProjectResourceAllocation',
    'app.filament.widgets.project-timeline-widget' => 'App\\Filament\\Widgets\\ProjectTimelineWidget',
    'app.filament.widgets.project-view-toggle' => 'App\\Filament\\Widgets\\ProjectViewToggle',
    'app.filament.widgets.quick-stats-widget' => 'App\\Filament\\Widgets\\QuickStatsWidget',
    'app.filament.widgets.recent-activities-widget' => 'App\\Filament\\Widgets\\RecentActivitiesWidget',
    'app.filament.widgets.recent-project-activity' => 'App\\Filament\\Widgets\\RecentProjectActivity',
    'app.filament.widgets.salary-analytics-widget' => 'App\\Filament\\Widgets\\SalaryAnalyticsWidget',
    'app.filament.widgets.salary-chart' => 'App\\Filament\\Widgets\\SalaryChart',
    'app.filament.widgets.schedule-table' => 'App\\Filament\\Widgets\\ScheduleTable',
    'app.filament.widgets.sop-overview' => 'App\\Filament\\Widgets\\SopOverview',
    'app.filament.widgets.supervisor-stats-overview' => 'App\\Filament\\Widgets\\SupervisorStatsOverview',
    'app.filament.widgets.system-overview-widget' => 'App\\Filament\\Widgets\\SystemOverviewWidget',
    'app.filament.widgets.team-performance-chart' => 'App\\Filament\\Widgets\\TeamPerformanceChart',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    0 => 'App\\Filament\\Pages\\GeneralLedger',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\AbsensiDashboard.php' => 'App\\Filament\\Pages\\AbsensiDashboard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\BalanceSheet.php' => 'App\\Filament\\Pages\\BalanceSheet',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\Dashboard.php' => 'App\\Filament\\Pages\\Dashboard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\EnhancedKanbanBoard.php' => 'App\\Filament\\Pages\\EnhancedKanbanBoard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\EnhancedProjectDashboard.php' => 'App\\Filament\\Pages\\EnhancedProjectDashboard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\GeneralLedger.php' => 'App\\Filament\\Pages\\GeneralLedger',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\HRDashboard.php' => 'App\\Filament\\Pages\\HRDashboard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\ImportSales.php' => 'App\\Filament\\Pages\\ImportSales',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\IncomeStatement.php' => 'App\\Filament\\Pages\\IncomeStatement',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\KanbanBoard.php' => 'App\\Filament\\Pages\\KanbanBoard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\PayrollDashboard.php' => 'App\\Filament\\Pages\\PayrollDashboard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\PerformanceDashboard.php' => 'App\\Filament\\Pages\\PerformanceDashboard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\ProjectOverview.php' => 'App\\Filament\\Pages\\ProjectOverview',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\SupervisorDashboard.php' => 'App\\Filament\\Pages\\SupervisorDashboard',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\Timesheets.php' => 'App\\Filament\\Pages\\Timesheets',
    'D:\\laragon\\www\\viera\\app\\Filament\\Pages\\WorkPeriodSettings.php' => 'App\\Filament\\Pages\\WorkPeriodSettings',
  ),
  'pageDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Pages',
  ),
  'resources' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\AbsensiResource.php' => 'App\\Filament\\Resources\\AbsensiResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\AkunResource.php' => 'App\\Filament\\Resources\\AkunResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\AsetResource.php' => 'App\\Filament\\Resources\\AsetResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\AturanKeterlambatanResource.php' => 'App\\Filament\\Resources\\AturanKeterlambatanResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\CompanySettingsResource.php' => 'App\\Filament\\Resources\\CompanySettingsResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\CutiIzinResource.php' => 'App\\Filament\\Resources\\CutiIzinResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\DepartemenResource.php' => 'App\\Filament\\Resources\\DepartemenResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\DivisiResource.php' => 'App\\Filament\\Resources\\DivisiResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\EntitasResource.php' => 'App\\Filament\\Resources\\EntitasResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\ExpenseCategoryResource.php' => 'App\\Filament\\Resources\\ExpenseCategoryResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\ExpenseRequestResource.php' => 'App\\Filament\\Resources\\ExpenseRequestResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\GoodsReceiptResource.php' => 'App\\Filament\\Resources\\GoodsReceiptResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\InventoryResource.php' => 'App\\Filament\\Resources\\InventoryResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\InventoryStockResource.php' => 'App\\Filament\\Resources\\InventoryStockResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\JabatanResource.php' => 'App\\Filament\\Resources\\JabatanResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\JadwalKerjaResource.php' => 'App\\Filament\\Resources\\JadwalKerjaResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\JadwalMasalResource.php' => 'App\\Filament\\Resources\\JadwalMasalResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\JenisPelanggaranResource.php' => 'App\\Filament\\Resources\\JenisPelanggaranResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\JournalResource.php' => 'App\\Filament\\Resources\\JournalResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\KaryawanResource.php' => 'App\\Filament\\Resources\\KaryawanResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\PayrollComponentResource.php' => 'App\\Filament\\Resources\\PayrollComponentResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\PayrollPeriodResource.php' => 'App\\Filament\\Resources\\PayrollPeriodResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\PayrollTransactionResource.php' => 'App\\Filament\\Resources\\PayrollTransactionResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\PettyCashFundResource.php' => 'App\\Filament\\Resources\\PettyCashFundResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\PostingRuleResource.php' => 'App\\Filament\\Resources\\PostingRuleResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\ProdukResource.php' => 'App\\Filament\\Resources\\ProdukResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\ProjectResource.php' => 'App\\Filament\\Resources\\ProjectResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\PtkpRateResource.php' => 'App\\Filament\\Resources\\PtkpRateResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\PurchaseOrderResource.php' => 'App\\Filament\\Resources\\PurchaseOrderResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\RoleResource.php' => 'App\\Filament\\Resources\\RoleResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\SalesTransactionResource.php' => 'App\\Filament\\Resources\\SalesTransactionResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\SatuanResource.php' => 'App\\Filament\\Resources\\SatuanResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\ShiftResource.php' => 'App\\Filament\\Resources\\ShiftResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\SopDokumenResource.php' => 'App\\Filament\\Resources\\SopDokumenResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\StockAdjustmentResource.php' => 'App\\Filament\\Resources\\StockAdjustmentResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\StockMovementResource.php' => 'App\\Filament\\Resources\\StockMovementResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\StockOpnameResource.php' => 'App\\Filament\\Resources\\StockOpnameResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\StrukturOrganisasiResource.php' => 'App\\Filament\\Resources\\StrukturOrganisasiResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\SupplierResource.php' => 'App\\Filament\\Resources\\SupplierResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\TaskResource.php' => 'App\\Filament\\Resources\\TaskResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\TaxBracketResource.php' => 'App\\Filament\\Resources\\TaxBracketResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\UnitResource.php' => 'App\\Filament\\Resources\\UnitResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\UserResource.php' => 'App\\Filament\\Resources\\UserResource',
    'D:\\laragon\\www\\viera\\app\\Filament\\Resources\\WarehouseResource.php' => 'App\\Filament\\Resources\\WarehouseResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Resources',
  ),
  'widgets' => 
  array (
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\AbsensiOverviewWidget.php' => 'App\\Filament\\Widgets\\AbsensiOverviewWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\AbsensiStatusWidget.php' => 'App\\Filament\\Widgets\\AbsensiStatusWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\AbsensiTrendChart.php' => 'App\\Filament\\Widgets\\AbsensiTrendChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\AttendanceAlertsWidget.php' => 'App\\Filament\\Widgets\\AttendanceAlertsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\AttendanceAnalyticsWidget.php' => 'App\\Filament\\Widgets\\AttendanceAnalyticsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\AttendanceByDepartmentWidget.php' => 'App\\Filament\\Widgets\\AttendanceByDepartmentWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\AttendanceOverview.php' => 'App\\Filament\\Widgets\\AttendanceOverview',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\AttendanceTrendsWidget.php' => 'App\\Filament\\Widgets\\AttendanceTrendsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\CompensationBreakdownWidget.php' => 'App\\Filament\\Widgets\\CompensationBreakdownWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\EmployeeDemographicsWidget.php' => 'App\\Filament\\Widgets\\EmployeeDemographicsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\EnhancedProjectStats.php' => 'App\\Filament\\Widgets\\EnhancedProjectStats',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\HRAlertsWidget.php' => 'App\\Filament\\Widgets\\HRAlertsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\HROverviewWidget.php' => 'App\\Filament\\Widgets\\HROverviewWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\HrDepartmentChart.php' => 'App\\Filament\\Widgets\\HrDepartmentChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\HrStatsWidget.php' => 'App\\Filament\\Widgets\\HrStatsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\InventoryStatsWidget.php' => 'App\\Filament\\Widgets\\InventoryStatsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\LateArrivalsWidget.php' => 'App\\Filament\\Widgets\\LateArrivalsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\PayrollAlertsWidget.php' => 'App\\Filament\\Widgets\\PayrollAlertsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\PayrollByDepartmentWidget.php' => 'App\\Filament\\Widgets\\PayrollByDepartmentWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\PayrollOverviewWidget.php' => 'App\\Filament\\Widgets\\PayrollOverviewWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\PayrollStatsOverview.php' => 'App\\Filament\\Widgets\\PayrollStatsOverview',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\PayrollTrendChart.php' => 'App\\Filament\\Widgets\\PayrollTrendChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\PayrollTrendsWidget.php' => 'App\\Filament\\Widgets\\PayrollTrendsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\PerformanceAnalyticsWidget.php' => 'App\\Filament\\Widgets\\PerformanceAnalyticsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\PerformanceGradeChart.php' => 'App\\Filament\\Widgets\\PerformanceGradeChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\PerformanceOverviewWidget.php' => 'App\\Filament\\Widgets\\PerformanceOverviewWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\ProjectCalendarView.php' => 'App\\Filament\\Widgets\\ProjectCalendarView',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\ProjectGanttChart.php' => 'App\\Filament\\Widgets\\ProjectGanttChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\ProjectHealthOverview.php' => 'App\\Filament\\Widgets\\ProjectHealthOverview',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\ProjectManagementWidget.php' => 'App\\Filament\\Widgets\\ProjectManagementWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\ProjectOverviewStats.php' => 'App\\Filament\\Widgets\\ProjectOverviewStats',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\ProjectResourceAllocation.php' => 'App\\Filament\\Widgets\\ProjectResourceAllocation',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\ProjectTimelineWidget.php' => 'App\\Filament\\Widgets\\ProjectTimelineWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\ProjectViewToggle.php' => 'App\\Filament\\Widgets\\ProjectViewToggle',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\QuickStatsWidget.php' => 'App\\Filament\\Widgets\\QuickStatsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\RecentActivitiesWidget.php' => 'App\\Filament\\Widgets\\RecentActivitiesWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\RecentProjectActivity.php' => 'App\\Filament\\Widgets\\RecentProjectActivity',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\SalaryAnalyticsWidget.php' => 'App\\Filament\\Widgets\\SalaryAnalyticsWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\SalaryChart.php' => 'App\\Filament\\Widgets\\SalaryChart',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\ScheduleTable.php' => 'App\\Filament\\Widgets\\ScheduleTable',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\SopOverview.php' => 'App\\Filament\\Widgets\\SopOverview',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\SupervisorStatsOverview.php' => 'App\\Filament\\Widgets\\SupervisorStatsOverview',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\SystemOverviewWidget.php' => 'App\\Filament\\Widgets\\SystemOverviewWidget',
    'D:\\laragon\\www\\viera\\app\\Filament\\Widgets\\TeamPerformanceChart.php' => 'App\\Filament\\Widgets\\TeamPerformanceChart',
  ),
  'widgetDirectories' => 
  array (
    0 => 'D:\\laragon\\www\\viera\\app\\Filament/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Widgets',
  ),
);