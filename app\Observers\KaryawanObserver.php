<?php

namespace App\Observers;

use App\Models\Karyawan;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class KaryawanObserver
{
    /**
     * Handle the Karyawan "created" event.
     */
    public function created(Karyawan $karyawan): void
    {
        // Jika karyawan belum terkait dengan user, buat user baru
        if (!$karyawan->id_user) {
            $this->createUserForKaryawan($karyawan);
        }
    }

    /**
     * Handle the Karyawan "updated" event.
     */
    public function updated(Karyawan $karyawan): void
    {
        // Jika email karyawan berubah, update email user
        if ($karyawan->isDirty('email') && $karyawan->id_user) {
            $user = User::find($karyawan->id_user);
            if ($user) {
                $user->update(['email' => $karyawan->email]);
            }
        }

        // Jika nama karyawan berubah, update nama user
        if ($karyawan->isDirty('nama_lengkap') && $karyawan->id_user) {
            $user = User::find($karyawan->id_user);
            if ($user) {
                $user->update(['name' => $karyawan->nama_lengkap]);
            }
        }

        // Jika karyawan belum terkait dengan user, buat user baru
        if (!$karyawan->id_user) {
            $this->createUserForKaryawan($karyawan);
        }
    }

    /**
     * Handle the Karyawan "deleted" event.
     */
    public function deleted(Karyawan $karyawan): void
    {
        // Jika karyawan dihapus, hapus juga user terkait
        if ($karyawan->id_user) {
            $user = User::find($karyawan->id_user);
            if ($user) {
                $user->delete();
            }
        }
    }

    /**
     * Handle the Karyawan "restored" event.
     */
    public function restored(Karyawan $karyawan): void
    {
        // Jika karyawan dipulihkan, pulihkan juga user terkait
        if ($karyawan->id_user) {
            User::withTrashed()->where('id', $karyawan->id_user)->restore();
        }
    }

    /**
     * Handle the Karyawan "force deleted" event.
     */
    public function forceDeleted(Karyawan $karyawan): void
    {
        // Jika karyawan dihapus permanen, hapus permanen juga user terkait
        if ($karyawan->id_user) {
            User::withTrashed()->where('id', $karyawan->id_user)->forceDelete();
        }
    }

    /**
     * Create a new user for the karyawan
     */
    private function createUserForKaryawan(Karyawan $karyawan): void
    {
        // Pastikan karyawan memiliki email
        if (empty($karyawan->email)) {
            return;
        }

        // Cek apakah email sudah digunakan
        $existingUser = User::where('email', $karyawan->email)->first();
        if ($existingUser) {
            // Jika email sudah digunakan, kaitkan karyawan dengan user yang sudah ada
            $karyawan->update(['id_user' => $existingUser->id]);

            // Update role user menjadi karyawan jika belum
            if ($existingUser->role !== 'karyawan') {
                $existingUser->update(['role' => 'karyawan']);
;            }

            // assign role karyawan
            $existingUser->assignRole('karyawan');

            return;
        }

        // assign role karyawan

        // Buat user baru
        $user = User::create([
            'name' => $karyawan->nama_lengkap,
            'email' => $karyawan->email,
            'password' => Hash::make('viera123'), // Default password
            'role' => 'karyawan',
            'karyawan_id' => $karyawan->id,
        ]);

        $user->assignRole('karyawan');

        // Kaitkan karyawan dengan user baru
        $karyawan->update(['id_user' => $user->id]);
    }
}
